# HMI与AD9959控制系统 Mermaid 流程图代码

## 1. 系统整体架构图

```mermaid
graph TB
    A[STM32F4 MCU] --> B[USART2<br/>115200波特率]
    B --> C[HMI设备]
    A --> D[AD9959<br/>频率合成器]
    A --> E[中断系统]
    
    subgraph "主要功能模块"
        F[串口通信模块]
        G[命令解析模块]
        H[频率控制模块]
        I[HMI显示模块]
    end
    
    A --> F
    F --> G
    G --> H
    G --> I
    H --> D
    I --> C
```

## 2. 系统初始化流程图

```mermaid
flowchart TD
    A[系统启动] --> B[HAL库初始化]
    B --> C[AD9959初始化]
    C --> D[串口2初始化<br/>115200波特率]
    D --> E[启用USART2中断<br/>优先级0]
    E --> F[设置初始频率和幅度]
    F --> G[启动串口中断接收]
    G --> H[进入主循环]
    H --> I{检查接收标志}
    I -->|有数据| J[处理命令]
    I -->|无数据| H
    J --> H
```

## 3. 串口中断接收流程图

```mermaid
flowchart TD
    A[串口接收中断] --> B[HAL_UART_RxCpltCallback]
    B --> C{是否第一个字节?}
    C -->|是| D[判断命令类型]
    C -->|否| E[存储到缓冲区]
    
    D --> F{命令类型}
    F -->|单字节命令<br/>'1','2','4','5','6'| G[设置接收完成标志]
    F -->|多字节命令<br/>'3'| H[继续接收4字节数据]
    F -->|握手信号<br/>0xfd| I[继续接收3字节]
    
    H --> E
    I --> E
    E --> J{接收完成?}
    J -->|是| G
    J -->|否| K[重新启动中断接收]
    G --> L[设置USART_RX_STA标志]
    L --> K
    K --> M[等待下次中断]
```

## 4. 主循环命令处理流程图

```mermaid
flowchart TD
    A[主循环] --> B{USART_RX_STA & 0x8000?}
    B -->|否| A
    B -->|是| C[获取接收长度<br/>len = USART_RX_STA & 0x3fff]
    C --> D{命令类型判断}
    
    D -->|握手信号| E[握手处理]
    D -->|频率命令| F[频率处理]
    D -->|浮点命令| G[浮点处理]
    D -->|波形命令| H[波形处理]
    
    E --> I[printf OKK]
    F --> J[更新combined值]
    G --> K[更新test_float值]
    H --> L[清除波形显示]
    
    J --> M[调用ad9959_write_frequency]
    J --> N[调用HMI_send_number]
    K --> O[调用HMI_send_float]
    L --> P[调用HMI_Wave_Clear]
    
    I --> Q[清除接收标志]
    M --> Q
    N --> Q
    O --> Q
    P --> Q
    Q --> R[memset清空缓冲区]
    R --> A
```

## 5. 具体命令处理分支图

```mermaid
flowchart TD
    A[接收到命令] --> B{命令类型}
    
    B -->|'1'| C[combined += 100]
    B -->|'2'| D[combined -= 100]
    B -->|'3'+4字节| E[解析4字节数据<br/>combined = 组合值]
    B -->|'4'| F[test_float += 0.1]
    B -->|'5'| G[test_float -= 0.1]
    B -->|'6'| H[清除波形]
    B -->|0xfd+3字节| I[握手确认]
    
    C --> J[ad9959_write_frequency<br/>AD9959_CHANNEL_0, combined]
    D --> J
    E --> J
    
    J --> K[HMI_send_number<br/>n0, combined]
    
    F --> L[HMI_send_float<br/>x0, test_float]
    G --> L
    
    H --> M[HMI_Wave_Clear]
    I --> N[printf OKK]
    
    K --> O[命令处理完成]
    L --> O
    M --> O
    N --> O
```

## 6. AD9959频率控制流程图

```mermaid
flowchart TD
    A[频率命令触发] --> B[计算新频率值]
    B --> C{频率范围检查<br/>1Hz - 200MHz}
    C -->|超出范围| D[使用默认值]
    C -->|范围内| E[使用计算值]
    D --> F[调用ad9959_write_frequency]
    E --> F
    F --> G[AD9959寄存器配置]
    G --> H[DDS核心更新]
    H --> I[输出新频率信号]
    I --> J[频率控制完成]
```

## 7. HMI通信协议流程图

```mermaid
flowchart TD
    A[HMI函数调用] --> B{函数类型}
    
    B -->|HMI_send_string| C[格式化字符串<br/>控件名.txt=值]
    B -->|HMI_send_number| D[格式化数字<br/>控件名.val=值]
    B -->|HMI_send_float| E[格式化浮点数<br/>控件名.val=值*100]
    B -->|HMI_Wave| F[格式化波形数据<br/>add 控件名,通道,值]
    B -->|HMI_Wave_Fast| G[格式化快速波形<br/>addt 控件名,通道,值]
    B -->|HMI_Wave_Clear| H[格式化清除命令<br/>cle 控件名,颜色]
    
    C --> I[添加结束符<br/>0xff 0xff 0xff]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[HAL_UART_Transmit<br/>串口2发送]
    J --> K[HMI设备接收]
    K --> L[HMI显示更新]
```

## 8. 错误处理流程图

```mermaid
flowchart TD
    A[系统运行] --> B{检测到错误?}
    B -->|否| A
    B -->|是| C{错误类型}
    
    C -->|接收缓冲区溢出| D[重置USART_RX_STA]
    C -->|无效命令| E[忽略命令]
    C -->|串口通信错误| F[重新初始化串口]
    C -->|AD9959通信错误| G[重新初始化AD9959]
    
    D --> H[清空接收缓冲区]
    E --> H
    F --> I[重新启动中断接收]
    G --> J[恢复默认频率设置]
    
    H --> I
    I --> K[错误恢复完成]
    J --> K
    K --> A
```

## 9. 测试验证流程图

```mermaid
flowchart TD
    A[开始测试] --> B[握手测试]
    B --> C[发送: 0xfd 0xff 0xff 0xff]
    C --> D{收到 OKK?}
    D -->|否| E[通信失败]
    D -->|是| F[频率控制测试]
    
    F --> G[发送 '1' 命令]
    G --> H{频率增加100Hz?}
    H -->|否| I[频率控制失败]
    H -->|是| J[发送 '2' 命令]
    
    J --> K{频率减少100Hz?}
    K -->|否| I
    K -->|是| L[发送 '3'+数据]
    
    L --> M{设置具体频率成功?}
    M -->|否| I
    M -->|是| N[浮点数测试]
    
    N --> O[发送 '4' 和 '5']
    O --> P{浮点数控制正常?}
    P -->|否| Q[浮点控制失败]
    P -->|是| R[波形测试]
    
    R --> S[发送 '6' 命令]
    S --> T{波形清除成功?}
    T -->|否| U[波形控制失败]
    T -->|是| V[所有测试通过]
    
    E --> W[检查硬件连接]
    I --> X[检查AD9959配置]
    Q --> Y[检查HMI连接]
    U --> Y
    V --> Z[系统就绪]
```

## 10. 数据流向图

```mermaid
flowchart LR
    A[外部设备] -->|串口命令| B[USART2接收]
    B --> C[中断处理]
    C --> D[接收缓冲区]
    D --> E[主循环解析]
    E --> F{命令类型}
    
    F -->|频率命令| G[combined变量]
    F -->|浮点命令| H[test_float变量]
    F -->|波形命令| I[HMI波形函数]
    F -->|握手命令| J[printf输出]
    
    G --> K[AD9959硬件]
    G --> L[HMI数字显示]
    H --> M[HMI浮点显示]
    I --> N[HMI波形显示]
    J --> O[串口2发送]
    
    K --> P[频率输出]
    L --> Q[HMI设备]
    M --> Q
    N --> Q
    O --> R[外部设备确认]
```

---

## 使用说明

1. **复制代码**: 将上述 Mermaid 代码复制到支持 Mermaid 的工具中
2. **推荐工具**: 
   - Mermaid Live Editor (https://mermaid.live/)
   - Draw.io (支持 Mermaid)
   - Typora (Markdown编辑器)
   - VS Code (安装 Mermaid 插件)
3. **自定义**: 可根据需要修改颜色、样式和布局
4. **导出**: 生成的图表可导出为 PNG、SVG 等格式
