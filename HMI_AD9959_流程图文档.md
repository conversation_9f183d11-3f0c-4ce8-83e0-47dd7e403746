# HMI与AD9959控制系统流程图文档

## 系统架构概览

### 主要组件
- **STM32F4 MCU** - 主控制器
- **HMI设备** - 人机界面显示
- **AD9959** - 直接数字频率合成器
- **串口通信** - USART2 (115200波特率)

## 系统初始化流程

```
系统启动
    ↓
HAL库初始化
    ↓
AD9959初始化
    ↓
串口2初始化 (115200波特率)
    ↓
启用USART2中断
    ↓
设置初始频率和幅度
    ↓
进入主循环
```

## 串口通信流程

### 接收流程
```
串口接收中断触发
    ↓
HAL_UART_RxCpltCallback()
    ↓
判断接收状态
    ├─ 第一个字节 → 判断命令类型
    │   ├─ 单字节命令 → 设置接收完成标志
    │   └─ 多字节命令 → 继续接收数据
    └─ 后续字节 → 存储到缓冲区
    ↓
接收完成 → 设置USART_RX_STA标志
    ↓
重新启动中断接收
```

### 发送流程
```
HMI发送函数调用
    ↓
选择发送类型
    ├─ HMI_send_string() → 发送字符串
    ├─ HMI_send_number() → 发送数字
    ├─ HMI_send_float() → 发送浮点数
    ├─ HMI_Wave() → 发送波形数据
    ├─ HMI_Wave_Fast() → 快速波形数据
    └─ HMI_Wave_Clear() → 清除波形
    ↓
HAL_UART_Transmit() 发送数据
    ↓
发送完成
```

## 命令处理流程

### 主循环命令处理
```
主循环检查
    ↓
USART_RX_STA & 0x8000 ? (接收完成标志)
    ↓ 是
获取接收长度: len = USART_RX_STA & 0x3fff
    ↓
根据命令类型分支处理
    ├─ 握手信号 (0xfd 0xff 0xff 0xff)
    ├─ 单字节命令 ('1', '2', '4', '5', '6')
    └─ 多字节命令 ('3' + 4字节数据)
    ↓
执行相应操作
    ↓
清除接收标志和缓冲区
    ↓
返回主循环
```

### 具体命令处理分支

#### 握手信号处理
```
接收: 0xfd 0xff 0xff 0xff
    ↓
printf("OKK")
    ↓
通过串口2发送确认
```

#### 频率控制命令 ('1', '2', '3')
```
命令解析
    ├─ '1' → combined += 100
    ├─ '2' → combined -= 100
    └─ '3' → combined = (num4<<24)|(num3<<16)|(num2<<8)|num1
    ↓
调用 ad9959_write_frequency(AD9959_CHANNEL_0, combined)
    ↓
调用 HMI_send_number("n0", combined)
    ↓
更新完成
```

#### 浮点数控制命令 ('4', '5')
```
命令解析
    ├─ '4' → test_float += 0.1
    └─ '5' → test_float -= 0.1
    ↓
调用 HMI_send_float("x0", test_float)
    ↓
更新完成
```

#### 波形清除命令 ('6')
```
接收命令 '6'
    ↓
调用 HMI_Wave_Clear()
    ↓
清除HMI波形显示
```

## AD9959控制流程

```
频率命令触发
    ↓
计算新频率值 (combined)
    ↓
调用 ad9959_write_frequency()
    ├─ 参数1: AD9959_CHANNEL_0
    └─ 参数2: combined (频率值)
    ↓
AD9959硬件更新
    ↓
输出新频率信号
```

## HMI更新流程

```
数据更新触发
    ↓
选择更新类型
    ├─ 频率更新 → HMI_send_number("n0", combined)
    ├─ 浮点数更新 → HMI_send_float("x0", test_float)
    └─ 波形清除 → HMI_Wave_Clear()
    ↓
格式化数据
    ↓
通过串口2发送到HMI
    ↓
HMI设备显示更新
```

## 错误处理流程

```
接收过程中
    ↓
检查缓冲区状态
    ├─ 接收长度 > 200 → 重置接收状态
    ├─ 无效命令 → 忽略并继续接收
    └─ 正常接收 → 继续处理
    ↓
自动恢复机制
    ↓
重新启动接收
```

## 测试验证流程

```
系统测试开始
    ↓
1. 握手测试
    ├─ 发送: 0xfd 0xff 0xff 0xff
    └─ 期望回复: "OKK"
    ↓
2. 频率控制测试
    ├─ 发送 '1' → 频率+100Hz
    ├─ 发送 '2' → 频率-100Hz
    └─ 发送 '3'+数据 → 设置具体频率
    ↓
3. 浮点数测试
    ├─ 发送 '4' → 浮点数+0.1
    └─ 发送 '5' → 浮点数-0.1
    ↓
4. 波形测试
    └─ 发送 '6' → 清除波形
    ↓
验证结果
    ├─ HMI显示更新
    ├─ AD9959频率输出
    └─ 串口通信正常
    ↓
测试完成
```

## 关键数据流

### 频率数据流
```
串口接收 → 命令解析 → combined变量 → AD9959硬件 → 频率输出
                    ↓
                HMI显示更新
```

### 浮点数据流
```
串口接收 → 命令解析 → test_float变量 → HMI显示更新
```

### 通信数据流
```
外部设备 → 串口2接收 → 中断处理 → 缓冲区 → 主循环处理 → 功能执行
                                                    ↓
HMI设备 ← 串口2发送 ← HMI函数调用 ← 状态更新 ← 功能执行完成
```

## 系统状态图

### 接收状态机
```
空闲状态 → 接收第一字节 → 判断命令类型
    ↓                        ↓
重新开始 ← 处理完成 ← 接收完成 ← 继续接收数据
```

### 处理状态机
```
等待命令 → 解析命令 → 执行操作 → 更新显示 → 等待命令
```

---

## 注意事项

1. **时序要求**: 中断处理要快速完成，避免阻塞
2. **数据完整性**: 多字节命令需要完整接收后再处理
3. **错误恢复**: 异常情况下自动重置接收状态
4. **硬件依赖**: 确保串口和AD9959硬件连接正确
